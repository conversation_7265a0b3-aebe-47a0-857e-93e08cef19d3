<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>调用测试</title>
  <script>
    async function callWebService () {
      const xmlRequest = `
                <Request>
                    <TradeCode>1012</TradeCode>
                    <HospitalId>SGSDYRMYY</HospitalId>
                    <ExtUserID>NOVA001</ExtUserID>
                </Request>
            `;

      const soapRequest = `
                <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                  <soap:Body>
                      <QueryDepartment xmlns="http://dhcc.com.cn">
                          <action>MES0018</action>
                          <message>
                            <Request>
                              <TradeCode>1012</TradeCode>
                              <HospitalId>SGSDYRMYY</HospitalId>
                              <ExtUserID>NOVA001</ExtUserID>
                             </Request>  
                          </message>
                      </QueryDepartment>
                  </soap:Body>
            </soap:Envelope>
            `.trim();

      try {
        const response = await fetch('/api/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS?WSDL=1', {
          method: 'POST',
          headers: {
            'Content-Type': 'text/xml; charset=utf-8',
            'SOAPAction': 'QueryDepartment'
          },
          body: soapRequest
        });

        const xmlText = await response.text();
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlText, "text/xml");

        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = `
                    <h3>请求成功!</h3>
                    <p>原始响应：</p>
                    <textarea style="width:100%;height:300px;font-family:monospace">${xmlText}</textarea>
                `;

      } catch (error) {
        document.getElementById('result').innerHTML = `<p style="color:red">请求失败: ${error}</p>`;
      }
    }
  </script>
</head>

<body>
  <button onclick="callWebService()">调用</button>
  <div id="result"></div>
</body>

</html>